package models

type TimesheetType string

const (
	TimesheetTypeProject  TimesheetType = "PROJECT"
	TimesheetTypeSga      TimesheetType = "SGA"
	TimesheetTypeLeave    TimesheetType = "LEAVE"
	TimesheetTypeInternal TimesheetType = "INTERNAL"
	TimesheetTypeExternal TimesheetType = "EXTERNAL"
	TimesheetTypeOt       TimesheetType = "OT"
)

type LeaveType string

const (
	LeaveTypeAnnual     LeaveType = "ANNUAL"
	LeaveTypeSick       LeaveType = "SICK"
	LeaveTypeBusiness   LeaveType = "BUSINESS"
	LeaveTypeMenstrual  LeaveType = "MENSTRUAL"
	LeaveTypeBirthday   LeaveType = "BIRTHDAY"
	LeaveTypeOrdination LeaveType = "ORDINATION"
)

type Timesheet struct {
	BaseModelHardDelete
	ProjectCode *string       `json:"project_code" gorm:"column:project_code"`
	SgaID       *string       `json:"sga_id" gorm:"column:sga_id"`
	UserID      string        `json:"user_id" gorm:"column:user_id"`
	Timing      float64       `json:"timing" gorm:"column:timing"`
	Type        TimesheetType `json:"type" gorm:"column:type"`
	LeaveType   *LeaveType    `json:"leave_type" gorm:"column:leave_type"`
	Description *string       `json:"description" gorm:"column:description"`
	Date        string        `json:"date" gorm:"column:date;type:date"`

	// Relations
	User    *User    `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Sga     *Sga     `json:"sga,omitempty" gorm:"foreignKey:SgaID"`
	Project *Project `json:"project,omitempty" gorm:"foreignKey:ProjectCode;references:Code"`
}

func (Timesheet) TableName() string {
	return "timesheets"
}

package repo

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type DepartmentOption func(repository.IRepository[models.Department])

var Department = func(c core.IContext, options ...DepartmentOption) repository.IRepository[models.Department] {
	r := repository.New[models.Department](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func DepartmentOrderBy(pageOptions *core.PageOptions) DepartmentOption {
	return func(c repository.IRepository[models.Department]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("name_th ASC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func DepartmentWithMinistry() DepartmentOption {
	return func(c repository.IRepository[models.Department]) {
		c.Preload("Ministry")
	}
}

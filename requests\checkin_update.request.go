package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type CheckinUpdate struct {
	core.BaseValidator
	Type     *string `json:"type"`
	Period   *string `json:"period"`
	Location *string `json:"location"`
	Remarks  *string `json:"remarks"`
	IsUnused *bool   `json:"is_unused"`
	Date     *string `json:"date"`
}

func (r *CheckinUpdate) Valid(ctx core.IContext) core.IError {
	if utils.ToNonPointer(r.Date) != "" {
		r.Must(r.IsDateTime(r.Date, "date"))
	}

	if r.Date == nil {
		r.Date = utils.ToPointer(utils.GetCurrentDateTime().String())
	}
	
	r.Must(r.IsStrIn(r.Type, strings.Join([]string{string(models.CheckinTypeOfficeHQ), string(models.CheckinTypeWfh),
		string(models.CheckinTypeOnsite), string(models.CheckinTypeOfficeAKV),
		string(models.CheckinTypeBusinessTrip), string(models.CheckinTypeAnnual),
		string(models.CheckinTypeSick), string(models.CheckinTypeMenstrual),
		string(models.CheckinTypeBirthday), string(models.CheckinTypeOrdination),
		string(models.CheckinTypeBusiness)}, "|"), "type"))
	if r.Must(r.IsStrRequired(r.Type, "type")) {
		if utils.ToNonPointer(r.Type) == string(models.CheckinTypeOnsite) || utils.ToNonPointer(r.Type) == string(models.CheckinTypeBusinessTrip) {
			r.Must(r.IsStrRequired(r.Location, "location"))

		}
		if utils.ToNonPointer(r.Type) == string(models.CheckinTypeAnnual) || utils.ToNonPointer(r.Type) == string(models.CheckinTypeSick) ||
			utils.ToNonPointer(r.Type) == string(models.CheckinTypeMenstrual) ||
			utils.ToNonPointer(r.Type) == string(models.CheckinTypeBirthday) ||
			utils.ToNonPointer(r.Type) == string(models.CheckinTypeOrdination) ||
			utils.ToNonPointer(r.Type) == string(models.CheckinTypeBusiness) {
			r.Must(r.IsStrRequired(r.Location, "period"))
			r.Must(r.IsStrIn(r.Period, strings.Join([]string{string(models.CheckinPeriodHalfMorning), string(models.CheckinPeriodHalfAfternoon),
				string(models.CheckinPeriodFullDay), string(models.CheckinPeriodManyDays)}, "|"), "period"))
		}
	}

	return r.Error()
}

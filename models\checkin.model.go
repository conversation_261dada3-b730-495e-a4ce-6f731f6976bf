package models

import "time"

type CheckinType string

const (
	CheckinTypeOfficeAKV    CheckinType = "OFFICE_AKV"
	CheckinTypeOfficeHQ     CheckinType = "OFFICE_HQ"
	CheckinTypeWfh          CheckinType = "WFH"
	CheckinTypeOnsite       CheckinType = "ONSITE"
	CheckinTypeBusinessTrip CheckinType = "BUSINESS_TRIP"
	CheckinTypeBusiness     CheckinType = "BUSINESS_LEAVE"
	CheckinTypeAnnual       CheckinType = "ANNUAL_LEAVE"
	CheckinTypeSick         CheckinType = "SICK_LEAVE"
	CheckinTypeMenstrual    CheckinType = "MENSTRUAL_LEAVE"
	CheckinTypeBirthday     CheckinType = "BIRTHDAY_LEAVE"
	CheckinTypeOrdination   CheckinType = "ORDINATION_LEAVE"
)

type CheckinPeriod string

const (
	CheckinPeriodHalfMorning   CheckinPeriod = "HALF_MORNING"
	CheckinPeriodHalfAfternoon CheckinPeriod = "HALF_AFTERNOON"
	CheckinPeriodFullDay       CheckinPeriod = "FULL_DAY"
	CheckinPeriodManyDays      CheckinPeriod = "MANY_DAYS"
)

type Checkin struct {
	BaseModelHardDelete
	UserId   string        `json:"user_id" gorm:"column:user_id;type:uuid"`
	Type     CheckinType   `json:"type" gorm:"column:type"`
	Period   CheckinPeriod `json:"period" gorm:"column:period"`
	Location *string       `json:"location" gorm:"column:location"`
	Remarks  *string       `json:"remarks" gorm:"column:remarks"`
	IsUnused *bool         `json:"is_unused" gorm:"column:is_unused;default:false"`
	Date     *time.Time    `json:"date" gorm:"column:date;type:date"`
	// Relations
	User *User `json:"user,omitempty" gorm:"foreignKey:UserId;references:ID;constraint:OnDelete:CASCADE"`
}

func (Checkin) TableName() string {
	return "checkins"
}

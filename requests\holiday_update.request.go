package requests

import (
	"time"
	core "gitlab.finema.co/finema/idin-core"
)

type HolidayUpdate struct {
	core.BaseValidator
	Name       *string    `json:"name"`
	Date       *time.Time `json:"date"`
	IsNational *bool      `json:"is_national"`
}

func (r *HolidayUpdate) Valid(ctx core.IContext) core.IError {
	// All fields are optional for updates, but if provided they should be valid	

	return r.Error()
}

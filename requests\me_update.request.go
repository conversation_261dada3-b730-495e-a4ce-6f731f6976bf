package requests

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type MeUpdate struct {
	core.BaseValidator
	FullName    *string `json:"full_name"`
	DisplayName *string `json:"display_name"`
	Position    *string `json:"position"`
	TeamCode    *string `json:"team_code"`
	AvatarURL   *string `json:"avatar_url"`
}

func (r *MeUpdate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsExists(ctx, r.TeamCode, models.Team{}.TableName(), "code", "team_code"))
	r.Must(r.IsURL(r.AvatarURL, "avatar_url"))
	return r.Error()
}

package models

type Department struct {
	BaseModel
	MinistryID string  `json:"ministry_id" gorm:"column:ministry_id;type:uuid"`
	NameTh     string  `json:"name_th" gorm:"column:name_th"`
	NameEn     *string `json:"name_en" gorm:"column:name_en"`

	// Relations
	Ministry *Ministry `json:"ministry,omitempty" gorm:"foreignKey:MinistryID;references:ID"`
}

func (Department) TableName() string {
	return "departments"
}

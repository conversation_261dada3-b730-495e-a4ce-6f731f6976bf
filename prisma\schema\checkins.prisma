enum CheckinType {
  WFH
  ONSITE
  OFFICE_AKV
  OFFICE_HQ
  BUSINESS_TRIP
  BUSINESS_LEAVE
  ANNUAL_LEAVE
  SICK_LEAVE
  MENSTRUAL_LEAVE
  BIRTHDAY_LEAVE
  ORDINATION_LEAVE
}

enum Period {
  HALF_MORNING
  HALF_AFTERNOON
  FULL_DAY
  MANY_DAYS
}

model checkins {
  id         String      @id @default(uuid()) @db.Uuid
  user_id    String      @db.Uuid
  type       CheckinType
  period     Period
  location   String
  remarks    String?
  is_unused  Boolean     @default(false)
  date       DateTime    @default(now())
  created_at DateTime    @default(now())
  updated_at DateTime    @default(now()) @updatedAt

  // Relations
  user users @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@index([type, location])
}

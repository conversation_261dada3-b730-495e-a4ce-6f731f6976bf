/*
  Warnings:

  - The values [OFFICE] on the enum `CheckinType` will be removed. If these variants are still used in the database, this will fail.

*/
-- AlterEnum
BEGIN;
CREATE TYPE "public"."CheckinType_new" AS ENUM ('WFH', 'ONSITE', 'OFFICE_AKV', 'OFFICE_HQ', 'BUSINESS_TRIP', 'BUSINESS_LEAVE', 'ANNUAL_LEAVE', 'SICK_LEAVE', 'MENSTRUAL_LEAVE', 'BIRTHDAY_LEAVE', 'ORDINATION_LEAVE');
ALTER TABLE "public"."checkins" ALTER COLUMN "type" TYPE "public"."CheckinType_new" USING ("type"::text::"public"."CheckinType_new");
ALTER TYPE "public"."CheckinType" RENAME TO "CheckinType_old";
ALTER TYPE "public"."CheckinType_new" RENAME TO "CheckinType";
DROP TYPE "public"."CheckinType_old";
COMMIT;

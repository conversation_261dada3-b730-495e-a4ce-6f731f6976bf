package checkin

import (
	"github.com/labstack/echo/v4"
	"gitlab.finema.co/finema/finework/finework-api/middleware"
	core "gitlab.finema.co/finema/idin-core"
)

func NewCheckinHTTP(e *echo.Echo) {
	checkin := &CheckinController{}
	e.GET("/checkins", core.WithHTTPContext(checkin.Pagination), middleware.AuthMiddleware())
	e.GET("/checkins/:id", core.WithHTTPContext(checkin.Find), middleware.AuthMiddleware())
	e.POST("/checkins", core.WithHTTPContext(checkin.Create), middleware.AuthMiddleware())
	e.PUT("/checkins/:id", core.WithHTTPContext(checkin.Update), middleware.AuthMiddleware())
	e.DELETE("/checkins/:id", core.WithHTTPContext(checkin.Delete), middleware.AuthMiddleware())

	checkinAdmin := &CheckinAdminController{}
	e.GET("/admin/checkins", core.WithHTTPContext(checkinAdmin.Pagination), middleware.AuthMiddleware())
	e.GET("/admin/checkins/:id", core.WithHTTPContext(checkinAdmin.Find), middleware.AuthMiddleware())
}

package repo

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type TeamOption func(repository.IRepository[models.Team])

var Team = func(c core.IContext, options ...TeamOption) repository.IRepository[models.Team] {
	r := repository.New[models.Team](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func TeamOrderBy(pageOptions *core.PageOptions) TeamOption {
	return func(c repository.IRepository[models.Team]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("name ASC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

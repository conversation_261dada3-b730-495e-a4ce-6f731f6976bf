package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type CheckinPaginationRequest struct {
	core.BaseValidator
	StartDate *string `json:"start_date" query:"start_date"`
	EndDate   *string `json:"end_date" query:"end_date"`
	TeamCode  *string `json:"team_code" query:"team_code"`
	UserID    *string `json:"user_id" query:"user_id"`
	Type      *string `json:"type" query:"type"`
}

func (r *CheckinPaginationRequest) Validate(ctx core.IContext) core.IError {
	r.Must(r.IsDate(r.StartDate, "start_date"))
	r.Must(r.IsDate(r.EndDate, "end_date"))

	// Validate team_code exists if provided
	if r.TeamCode != nil {
		r.Must(r.IsExists(ctx, r.TeamCode, models.Team{}.TableName(), "code", "team_code"))
	}

	// Validate user_id exists if provided
	if r.UserID != nil {
		r.Must(r.IsExists(ctx, r.UserID, models.User{}.TableName(), "id", "user_id"))
	}

	// Validate type if provided
	if r.Type != nil {
		validTypes := []string{
			string(models.CheckinTypeOfficeHQ), string(models.CheckinTypeWfh),
			string(models.CheckinTypeOnsite), string(models.CheckinTypeOfficeAKV),
			string(models.CheckinTypeBusinessTrip), string(models.CheckinTypeAnnual),
			string(models.CheckinTypeSick), string(models.CheckinTypeMenstrual),
			string(models.CheckinTypeBirthday), string(models.CheckinTypeOrdination),
			string(models.CheckinTypeBusiness),
		}
		r.Must(r.IsStrIn(r.Type, strings.Join(validTypes, "|"), "type"))
	}

	return r.Error()
}

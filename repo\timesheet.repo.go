package repo

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gorm.io/gorm/clause"
)

type TimesheetOption func(repository.IRepository[models.Timesheet])

var Timesheet = func(c core.IContext, options ...TimesheetOption) repository.IRepository[models.Timesheet] {
	r := repository.New[models.Timesheet](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func TimesheetOrderBy(pageOptions *core.PageOptions) TimesheetOption {
	return func(c repository.IRepository[models.Timesheet]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func TimesheetWithUser(id *string) TimesheetOption {
	return func(c repository.IRepository[models.Timesheet]) {
		if id == nil {
			return
		}
		c.Where("user_id = ?", id)
	}
}

func TimesheetWithDateRange(startDate *string, endDate *string) TimesheetOption {
	return func(c repository.IRepository[models.Timesheet]) {
		if startDate != nil && endDate != nil {
			c.Where("date BETWEEN ? AND ?", startDate, endDate)
		} else if startDate != nil {
			c.Where("date >= ?", startDate)
		} else if endDate != nil {
			c.Where("date <= ?", endDate)
		}
	}
}
func TimesheetWithAllRelation() TimesheetOption {
	return func(c repository.IRepository[models.Timesheet]) {
		c.Preload(clause.Associations)
	}
}

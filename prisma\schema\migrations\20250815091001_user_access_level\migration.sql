/*
  Warnings:

  - You are about to drop the column `role` on the `users` table. All the data in the column will be lost.
  - You are about to drop the `user_permissions` table. If the table is not empty, all the data it contains will be lost.

*/
-- CreateEnum
CREATE TYPE "public"."PermissionLevel" AS ENUM ('NONE', 'USER', 'ADMIN', '<PERSON>UPER');

-- DropForeignKey
ALTER TABLE "public"."user_permissions" DROP CONSTRAINT "user_permissions_user_id_fkey";

-- DropIndex
DROP INDEX "public"."users_email_team_code_role_idx";

-- AlterTable
ALTER TABLE "public"."users" DROP COLUMN "role";

-- DropTable
DROP TABLE "public"."user_permissions";

-- DropEnum
DROP TYPE "public"."Permission";

-- DropEnum
DROP TYPE "public"."PermissionModule";

-- DropEnum
DROP TYPE "public"."Role";

-- CreateTable
CREATE TABLE "public"."user_access_levels" (
    "user_id" UUID NOT NULL,
    "clockin" "public"."PermissionLevel" NOT NULL DEFAULT 'USER',
    "timesheet" "public"."PermissionLevel" NOT NULL DEFAULT 'USER',
    "pmo" "public"."PermissionLevel" NOT NULL DEFAULT 'NONE',
    "setting" "public"."PermissionLevel" NOT NULL DEFAULT 'NONE',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_access_levels_pkey" PRIMARY KEY ("user_id")
);

-- CreateIndex
CREATE INDEX "users_email_team_code_idx" ON "public"."users"("email", "team_code");

-- AddForeignKey
ALTER TABLE "public"."user_access_levels" ADD CONSTRAINT "user_access_levels_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

model teams {
  id               String    @id @default(uuid()) @db.Uuid
  name             String    @unique
  code             String    @unique
  description      String?
  color            String    @default("")
  working_start_at DateTime? @db.Time()
  working_end_at   DateTime? @db.Time()

  created_at DateTime @default(now())
  updated_at DateTime @default(now()) @updatedAt

  // Relations
  users users[]

  @@index([name, code])
}

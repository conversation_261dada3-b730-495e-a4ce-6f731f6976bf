package requests

import (
	"strings"

	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/utils"
)

type TimesheetUpdate struct {
	core.BaseValidator
	ProjectCode *string  `json:"project_code"`
	SgaID       *string  `json:"sga_id"`
	Timing      *float64 `json:"timing"`
	Type        *string  `json:"type"`
	LeaveType   *string  `json:"leave_type"`
	Description *string  `json:"description"`
	Date        *string  `json:"date"`
}

func (r *TimesheetUpdate) Valid(ctx core.IContext) core.IError {
	if r.Must(r.IsRequired(r.Type, "type")) {
		switch utils.ToNonPointer(r.Type) {
		case string(models.TimesheetTypeLeave):
			r.Must(r.IsStrRequired(r.LeaveType, "leave_type"))
			r.Must(r.Is<PERSON>trIn(r.LeaveType, strings.Join([]string{string(models.LeaveTypeAnnual), string(models.LeaveTypeSick),
				string(models.LeaveTypeBusiness), string(models.LeaveTypeMenstrual),
				string(models.LeaveTypeBirthday), string(models.LeaveTypeOrdination)}, "|"), "leave_type"))
		case string(models.TimesheetTypeProject):
			r.Must(r.IsStrRequired(r.ProjectCode, "project_code"))
		case string(models.TimesheetTypeSga):
			r.Must(r.IsStrRequired(r.SgaID, "sga_id"))
		}
	}
	r.Must(r.IsRequired(r.Date, "date"))
	r.Must(r.IsRequired(r.Timing, "timing"))
	r.Must(r.IsStrIn(r.Type, strings.Join([]string{string(models.TimesheetTypeProject), string(models.TimesheetTypeSga),
		string(models.TimesheetTypeLeave), string(models.TimesheetTypeInternal),
		string(models.TimesheetTypeExternal), string(models.TimesheetTypeOt)}, "|"), "type"))

	return r.Error()
}
